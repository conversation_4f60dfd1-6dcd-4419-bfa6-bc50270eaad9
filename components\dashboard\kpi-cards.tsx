"use client"

import { cn } from "@/lib/utils"

interface KPICardsProps {
  kpis: {
    thisMonthActivities: number
    lastMonthActivities: number
    thisMonthAvgAttendance: number
    attendanceChange: number
    engagementRate: number
    activeParticipants: number
  }
  averageAttendanceRate: number
  allParticipantsCount: number
}

export function KPICards({ kpis, averageAttendanceRate, allParticipantsCount }: KPICardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow p-4 text-white">
        <h3 className="text-sm font-medium opacity-90">本月活動數</h3>
        <div className="flex items-end justify-between">
          <p className="text-2xl font-bold">{kpis.thisMonthActivities}</p>
          <div className="text-right">
            <p className="text-xs opacity-75">上月: {kpis.lastMonthActivities}</p>
            <p className={cn(
              "text-xs font-medium",
              kpis.thisMonthActivities >= kpis.lastMonthActivities ? "text-green-200" : "text-red-200"
            )}>
              {kpis.thisMonthActivities >= kpis.lastMonthActivities ? "↗" : "↘"}
              {Math.abs(kpis.thisMonthActivities - kpis.lastMonthActivities)}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow p-4 text-white">
        <h3 className="text-sm font-medium opacity-90">本月平均出席率</h3>
        <div className="flex items-end justify-between">
          <p className="text-2xl font-bold">{kpis.thisMonthAvgAttendance}%</p>
          <div className="text-right">
            <p className={cn(
              "text-xs font-medium",
              kpis.attendanceChange >= 0 ? "text-green-200" : "text-red-200"
            )}>
              {kpis.attendanceChange >= 0 ? "↗" : "↘"} {Math.abs(kpis.attendanceChange).toFixed(1)}%
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow p-4 text-white">
        <h3 className="text-sm font-medium opacity-90">參與度</h3>
        <div className="flex items-end justify-between">
          <p className="text-2xl font-bold">{kpis.engagementRate}%</p>
          <div className="text-right">
            <p className="text-xs opacity-75">{kpis.activeParticipants}/{allParticipantsCount}</p>
            <p className="text-xs font-medium text-purple-200">活躍參與者</p>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow p-4 text-white">
        <h3 className="text-sm font-medium opacity-90">整體出席率</h3>
        <div className="flex items-end justify-between">
          <p className="text-2xl font-bold">{averageAttendanceRate.toFixed(1)}%</p>
          <div className="text-right">
            <p className="text-xs opacity-75">目標: 80%</p>
            <p className={cn(
              "text-xs font-medium",
              averageAttendanceRate >= 80 ? "text-green-200" : "text-yellow-200"
            )}>
              {averageAttendanceRate >= 80 ? "達標" : "待改善"}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
