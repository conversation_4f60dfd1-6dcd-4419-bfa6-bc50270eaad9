"use client"

import { cn } from "@/lib/utils"

interface TrendChartsProps {
  monthlyData: {
    month: string
    activities: number
    attendance: number
    participants: number
  }[]
  weeklyAttendance: {
    week: string
    rate: number
  }[]
}

export function TrendCharts({ monthlyData, weeklyAttendance }: TrendChartsProps) {
  // 計算最大值用於圖表縮放
  const maxActivities = Math.max(...monthlyData.map(d => d.activities))
  const maxAttendance = Math.max(...monthlyData.map(d => d.attendance))
  const maxParticipants = Math.max(...monthlyData.map(d => d.participants))
  const maxWeeklyRate = Math.max(...weeklyAttendance.map(d => d.rate))

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 月度趨勢圖 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">月度趨勢</h3>
        <div className="space-y-4">
          {monthlyData.map((data, index) => (
            <div key={data.month} className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600 dark:text-gray-300">{data.month}</span>
                <div className="flex space-x-4 text-xs">
                  <span className="text-blue-600 dark:text-blue-400">活動: {data.activities}</span>
                  <span className="text-green-600 dark:text-green-400">出席: {data.attendance}%</span>
                  <span className="text-purple-600 dark:text-purple-400">參與者: {data.participants}</span>
                </div>
              </div>
              
              {/* 活動數量條形圖 */}
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-blue-600 dark:text-blue-400 w-12">活動</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(data.activities / maxActivities) * 100}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-green-600 dark:text-green-400 w-12">出席</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${data.attendance}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-purple-600 dark:text-purple-400 w-12">參與</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(data.participants / maxParticipants) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 週度出席率趨勢 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">週度出席率趨勢</h3>
        <div className="space-y-3">
          {weeklyAttendance.map((data, index) => {
            const isIncreasing = index > 0 && data.rate > weeklyAttendance[index - 1].rate
            const isDecreasing = index > 0 && data.rate < weeklyAttendance[index - 1].rate
            
            return (
              <div key={data.week} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600 dark:text-gray-300 w-16">{data.week}</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-3 w-32">
                    <div
                      className={cn(
                        "h-3 rounded-full transition-all duration-300",
                        data.rate >= 80 ? "bg-green-500" :
                        data.rate >= 60 ? "bg-yellow-500" : "bg-red-500"
                      )}
                      style={{ width: `${data.rate}%` }}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {data.rate}%
                  </span>
                  {index > 0 && (
                    <span className={cn(
                      "text-xs",
                      isIncreasing ? "text-green-500" :
                      isDecreasing ? "text-red-500" : "text-gray-400"
                    )}>
                      {isIncreasing ? "↗" : isDecreasing ? "↘" : "→"}
                    </span>
                  )}
                </div>
              </div>
            )
          })}
        </div>
        
        {/* 趨勢總結 */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <div className="flex justify-between">
              <span>平均出席率:</span>
              <span className="font-medium">
                {(weeklyAttendance.reduce((sum, d) => sum + d.rate, 0) / weeklyAttendance.length).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between mt-1">
              <span>最高出席率:</span>
              <span className="font-medium text-green-600 dark:text-green-400">
                {Math.max(...weeklyAttendance.map(d => d.rate))}%
              </span>
            </div>
            <div className="flex justify-between mt-1">
              <span>最低出席率:</span>
              <span className="font-medium text-red-600 dark:text-red-400">
                {Math.min(...weeklyAttendance.map(d => d.rate))}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
