import type { ActivityLevelSettings } from "../../types"

interface Statistics {
  participationStats: {
    averageParticipationRate: number
    sessionParticipationStats: {
      [sessionId: string]: {
        averageParticipationRate: number
        sessionName: string
        activeParticipants: number
      }
    }
    highParticipationStats: {
      count: number
      threshold: number
      percentage: number
    }
  }
  participantStats: Array<{
    participationRate: number
  }>
}

interface ParticipantManagementStatsProps {
  activityLevelSettings: ActivityLevelSettings
  statistics: Statistics
  selectedSessionId: string | null
}

/**
 * 參加者管理統計信息組件
 * 用途：顯示活躍設定和參與度統計信息
 */
export function ParticipantManagementStats({
  activityLevelSettings,
  statistics,
  selectedSessionId,
}: ParticipantManagementStatsProps) {
  return (
    <>
      {/* 活躍設定顯示 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
        <div className="text-sm text-blue-800 dark:text-blue-200">
          <strong>當前活躍設定：</strong>
          非常活躍 ≥ {activityLevelSettings.veryActive}%， 活躍 {activityLevelSettings.active}%-
          {activityLevelSettings.veryActive - 1}%， 不活躍 &lt; {activityLevelSettings.active}%
        </div>
      </div>

      {/* 參與度統計 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">參與度統計</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300">整體平均參與度</h4>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {statistics.participationStats.averageParticipationRate.toFixed(1)}%
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              成員參與活動的平均比例
            </p>
          </div>

          {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-700 dark:text-green-300">當前屆別參與度</h4>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1)}%
              </p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName}
              </p>
            </div>
          )}

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">活躍成員數</h4>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId]
                ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants
                : statistics.participantStats.filter(p => p.participationRate > 0).length}
            </p>
            <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
              參與過活動的成員
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-orange-700 dark:text-orange-300">高參與度成員</h4>
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {statistics.participationStats.highParticipationStats.count}
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              ≥{statistics.participationStats.highParticipationStats.threshold}% ({statistics.participationStats.highParticipationStats.percentage.toFixed(1)}%)
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
