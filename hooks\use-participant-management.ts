import { useMemo } from "react"
import type { Participant, SessionParticipant, Session, Activity, ActivityLevelSettings } from "../types"
import { getActivityLevel } from "../utils/activity-level"
import { calculateStatistics } from "../utils/statistics"

interface UseParticipantManagementProps {
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  sessions: Session[]
  selectedSessionId: string | null
  activities: Activity[]
  activityLevelSettings: ActivityLevelSettings
  searchTerm: string
  categoryFilter: string
  statusFilter: "all" | "active" | "inactive"
  sortField: string
  sortDirection: "asc" | "desc"
}

export function useParticipantManagement({
  allParticipants,
  sessionParticipants,
  sessions,
  selectedSessionId,
  activities,
  activityLevelSettings,
  searchTerm,
  categoryFilter,
  statusFilter,
  sortField,
  sortDirection,
}: UseParticipantManagementProps) {
  // 獲取當前屆別
  const currentSession = useMemo(() => {
    return sessions.find((s) => s.id === selectedSessionId)
  }, [sessions, selectedSessionId])

  // 獲取當前屆別的參加者
  const currentSessionParticipants = useMemo(() => {
    return sessionParticipants.filter((sp) => sp.sessionId === selectedSessionId)
  }, [sessionParticipants, selectedSessionId])

  // 獲取當前屆別的所有職銜
  const currentSessionCategories = useMemo(() => {
    const categories = new Set(currentSessionParticipants.map((sp) => sp.category).filter(Boolean))
    return Array.from(categories)
  }, [currentSessionParticipants])

  // 獲取全局所有職銜
  const allCategories = useMemo(() => {
    const globalCategories = new Set(allParticipants.map((p) => p.category).filter(Boolean))
    const sessionCategories = new Set(sessionParticipants.map((sp) => sp.category).filter(Boolean))
    return Array.from(new Set([...globalCategories, ...sessionCategories]))
  }, [allParticipants, sessionParticipants])

  // 計算統計數據
  const statistics = useMemo(() => {
    return calculateStatistics(activities, allParticipants, sessions)
  }, [activities, allParticipants, sessions])

  // 過濾和排序參加者
  const filteredSessionParticipants = useMemo(() => {
    const filtered = currentSessionParticipants.filter((sp) => {
      const matchesSearch = sp.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = categoryFilter ? sp.category === categoryFilter : true
      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "active" && sp.isActive) ||
        (statusFilter === "inactive" && !sp.isActive)
      return matchesSearch && matchesCategory && matchesStatus
    })

    // 計算每個參加者的出席率和活躍等級
    const participantsWithActivityLevel = filtered.map((sp) => {
      // 獲取該參加者在當前屆別的所有活動
      const participantActivities = activities.filter((activity) => activity.sessionId === selectedSessionId)

      // 計算該參加者參與的活動數量和出席數量
      let totalParticipated = 0
      let totalAttended = 0

      participantActivities.forEach((activity) => {
        // 檢查該參加者是否參與了這個活動
        const participantInActivity = activity.participants.find((p) => p.id === sp.participantId)
        if (participantInActivity) {
          totalParticipated++

          // 檢查出席記錄 - 嘗試多種可能的鍵
          const attendanceRecord = participantInActivity.attendance
          let isAttended = false

          // 嘗試不同的鍵格式
          if (attendanceRecord[activity.id] === true) {
            isAttended = true
          } else if (attendanceRecord[activity.date] === true) {
            isAttended = true
          } else if (attendanceRecord[`${activity.date}`] === true) {
            isAttended = true
          }

          if (isAttended) {
            totalAttended++
          }
        }
      })

      const attendanceRate = totalParticipated > 0 ? (totalAttended / totalParticipated) * 100 : 0
      const activityLevel = getActivityLevel(attendanceRate, activityLevelSettings)

      return {
        ...sp,
        attendanceRate,
        activityLevel,
        totalParticipated,
        totalAttended,
      }
    })

    // 排序邏輯
    if (sortField) {
      participantsWithActivityLevel.sort((a, b) => {
        let aValue: any = ""
        let bValue: any = ""

        switch (sortField) {
          case "name":
            aValue = a.name.toLowerCase()
            bValue = b.name.toLowerCase()
            break
          case "category":
            aValue = a.category.toLowerCase()
            bValue = b.category.toLowerCase()
            break
          case "joinDate":
            aValue = a.joinDate || ""
            bValue = b.joinDate || ""
            break
          case "status":
            aValue = a.isActive ? "active" : "inactive"
            bValue = b.isActive ? "active" : "inactive"
            break
          case "attendanceRate":
            aValue = a.attendanceRate
            bValue = b.attendanceRate
            break
          default:
            return 0
        }

        if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
        if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
        return 0
      })
    }

    return participantsWithActivityLevel
  }, [
    currentSessionParticipants,
    searchTerm,
    categoryFilter,
    statusFilter,
    sortField,
    sortDirection,
    activities,
    selectedSessionId,
    activityLevelSettings,
  ])

  // 獲取不在當前屆別的全局參加者
  const availableGlobalParticipants = useMemo(() => {
    const sessionParticipantIds = new Set(currentSessionParticipants.map((sp) => sp.participantId))
    return allParticipants.filter((p) => !sessionParticipantIds.has(p.id))
  }, [allParticipants, currentSessionParticipants])

  return {
    currentSession,
    currentSessionParticipants,
    currentSessionCategories,
    allCategories,
    statistics,
    filteredSessionParticipants,
    availableGlobalParticipants,
  }
}
