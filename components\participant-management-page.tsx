"use client"

import { useState, useMemo } from "react"
import { cn } from "@/lib/utils"
import type { Participant, SessionParticipant, Session, Activity, ActivityLevelSettings } from "../types"
import { UniversalBulkImport } from "./universal-bulk-import"
import { ParticipantAttendanceHistory } from "./participant-attendance-history"
import { ParticipantTable } from "./participant-management/participant-table"
import { AddExistingParticipantDialog } from "./participant-management/add-existing-participant-dialog"
import { TitleManagementSection } from "./participant-management/title-management-section"
import { useParticipantManagement } from "../hooks/use-participant-management"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS, getActivityLevelText, getActivityLevelColor } from "../utils/activity-level"
import { calculateStatistics } from "../utils/statistics"

interface ParticipantManagementPageProps {
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  sessions: Session[]
  selectedSessionId: string | null
  onAddParticipant: (participant: Omit<Participant, "id" | "attendance">) => void
  onAddSessionParticipant: (sessionParticipant: Omit<SessionParticipant, "id">) => void
  onBulkAddSessionParticipants: (sessionParticipants: Omit<SessionParticipant, "id">[]) => void
  onBulkAddParticipants: (participants: Omit<Participant, "id" | "attendance">[]) => void
  onUpdateParticipant: (participant: Participant) => void
  onUpdateSessionParticipant: (sessionParticipant: SessionParticipant) => void
  onDeleteParticipant: (participantId: string) => void
  onRemoveFromSession: (sessionParticipantId: string) => void
  onBulkDeleteTitle: (title: string) => { participantsUpdated: number; placeholdersRemoved: number; sessionParticipantsUpdated: number }
  onBack: () => void
  activities: Activity[]
  activityLevelSettings?: ActivityLevelSettings
}

export function ParticipantManagementPage({
  allParticipants,
  sessionParticipants,
  sessions,
  selectedSessionId,
  onAddParticipant,
  onBulkAddSessionParticipants,
  onBulkAddParticipants,
  onUpdateParticipant,
  onUpdateSessionParticipant,
  onDeleteParticipant,
  onRemoveFromSession,
  onBulkDeleteTitle,
  onBack,
  activities,
  activityLevelSettings = DEFAULT_ACTIVITY_LEVEL_SETTINGS,
}: ParticipantManagementPageProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all")
  const [showBulkImport, setShowBulkImport] = useState(false)
  const [viewMode, setViewMode] = useState<"session" | "global">("session")
  const [viewingAttendanceHistory, setViewingAttendanceHistory] = useState<Participant | null>(null)
  const [showTitleManagement, setShowTitleManagement] = useState(false)
  const [showAddExisting, setShowAddExisting] = useState(false)
  const [sortField, setSortField] = useState<string>("")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

  // 使用自定義 Hook 處理參加者管理邏輯
  const {
    currentSession,
    currentSessionParticipants,
    currentSessionCategories,
    allCategories,
    statistics,
    filteredSessionParticipants,
    availableGlobalParticipants,
  } = useParticipantManagement({
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    activities,
    activityLevelSettings,
    searchTerm,
    categoryFilter,
    statusFilter,
    sortField,
    sortDirection,
  })

  // 排序處理函數
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // 批量添加選中的參加者到當前屆別
  const handleBatchAddToSession = (selectedParticipants: Set<string>) => {
    if (selectedSessionId && selectedParticipants.size > 0) {
      const sessionParticipantsToAdd: Omit<SessionParticipant, "id">[] = []
      const skippedParticipants: string[] = []

      selectedParticipants.forEach((participantId) => {
        const participant = allParticipants.find((p) => p.id === participantId)
        if (participant) {
          // 檢查該參加者是否已經在當前屆別中
          const existingSessionParticipant = sessionParticipants.find(
            sp => sp.participantId === participant.id && sp.sessionId === selectedSessionId
          )

          if (existingSessionParticipant) {
            skippedParticipants.push(participant.name)
          } else {
            sessionParticipantsToAdd.push({
              participantId: participant.id,
              sessionId: selectedSessionId,
              name: participant.name,
              category: participant.category || "",
              joinDate: new Date().toISOString().split("T")[0],
              isActive: true,
            })
          }
        }
      })

      // 使用批量添加函數
      if (sessionParticipantsToAdd.length > 0) {
        onBulkAddSessionParticipants(sessionParticipantsToAdd)

        let message = `成功添加 ${sessionParticipantsToAdd.length} 位成員到當前屆別`
        if (skippedParticipants.length > 0) {
          message += `\n\n跳過已存在的成員：${skippedParticipants.join(', ')}`
        }
        alert(message)
      } else if (skippedParticipants.length > 0) {
        alert(`所有選中的成員都已經在當前屆別中：${skippedParticipants.join(', ')}`)
      }

      setShowAddExisting(false)
    }
  }

  // 處理新增參加者
  const handleAddParticipant = async () => {
    if (newParticipant.name.trim() && !isAddingParticipant) {
      setIsAddingParticipant(true)
      try {
        const trimmedName = newParticipant.name.trim()

        // 檢查是否已存在相同姓名的參加者
        const existingParticipant = allParticipants.find(
          p => p.name.toLowerCase() === trimmedName.toLowerCase()
        )

        if (existingParticipant) {
          alert(`參加者「${trimmedName}」已存在，請使用不同的姓名`)
          return
        }

        // 處理新增職銜的情況
        let finalCategory = newParticipant.category
        if (finalCategory === "新增職銜") {
          finalCategory = newParticipantTitle.trim()
        }

        const participantToAdd = {
          name: trimmedName,
          category: finalCategory
        }

        onAddParticipant(participantToAdd)
        setNewParticipant({ name: "", category: "" })
        setNewParticipantTitle("")

        alert(`成功新增參加者「${participantToAdd.name}」${finalCategory ? `，職銜：${finalCategory}` : ''}`)
      } catch (error) {
        console.error('新增參加者時發生錯誤:', error)
        alert(`新增參加者時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
      } finally {
        setIsAddingParticipant(false)
      }
    }
  }

  // 處理更新參加者
  const handleUpdateParticipant = () => {
    if (editingParticipant) {
      onUpdateParticipant(editingParticipant)
      setEditingParticipant(null)
    }
  }

  // 處理更新屆別參加者
  const handleUpdateSessionParticipant = () => {
    if (editingSessionParticipant) {
      onUpdateSessionParticipant(editingSessionParticipant)
      setEditingSessionParticipant(null)
    }
  }

  // 處理批量導入
  const handleBulkImport = (participants: Omit<Participant, "id" | "attendance">[]) => {
    onBulkAddParticipants(participants)
    setShowBulkImport(false)
  }

  // 處理職銜編輯
  const handleUpdateTitle = (oldTitle: string, newTitle: string) => {
    if (newTitle.trim() && newTitle !== oldTitle) {
      try {
        // 檢查新職銜是否已存在（不區分大小寫）
        if (allCategories.some(category =>
          category.toLowerCase() === newTitle.trim().toLowerCase() &&
          category.toLowerCase() !== oldTitle.toLowerCase()
        )) {
          alert(`職銜「${newTitle.trim()}」已存在`)
          setEditingTitle("")
          return
        }

        // 找出所有使用該職銜的參加者
        const participantsToUpdate = allParticipants.filter(p =>
          p.category === oldTitle &&
          !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者
        )

        // 找出佔位符參加者（如果有）
        const placeholders = allParticipants.filter(p =>
          p.category === oldTitle &&
          p.name.startsWith('職銜佔位符-')
        )

        // 找出所有使用該職銜的屆別參加者
        const sessionParticipantsToUpdate = sessionParticipants.filter(sp =>
          sp.category === oldTitle
        )

        // 顯示進度提示
        const totalUpdates = participantsToUpdate.length
        const hasPlaceholders = placeholders.length > 0

        // 確認是否繼續
        if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {
          if (!window.confirm(`將更新職銜從「${oldTitle}」到「${newTitle}」，${totalUpdates > 0 ? `影響 ${totalUpdates} 位參加者` : ''}。\n\n確定繼續嗎？`)) {
            setEditingTitle("")
            return
          }

          // 批量更新全局參加者
          if (participantsToUpdate.length > 0) {
            const batchUpdatePromises = participantsToUpdate.map(participant => {
              const updatedParticipant = { ...participant, category: newTitle }
              return new Promise(resolve => {
                onUpdateParticipant(updatedParticipant)
                resolve(null)
              })
            })

            Promise.all(batchUpdatePromises).catch(err => {
              console.error('批量更新參加者時發生錯誤:', err)
            })
          }

          // 批量更新佔位符參加者
          if (placeholders.length > 0) {
            const batchUpdatePlaceholderPromises = placeholders.map(placeholder => {
              const updatedPlaceholder = {
                ...placeholder,
                name: `職銜佔位符-${newTitle}`,
                category: newTitle
              }
              return new Promise(resolve => {
                onUpdateParticipant(updatedPlaceholder)
                resolve(null)
              })
            })

            Promise.all(batchUpdatePlaceholderPromises).catch(err => {
              console.error('批量更新佔位符時發生錯誤:', err)
            })
          }

          // 批量更新屆別參加者
          if (sessionParticipantsToUpdate.length > 0) {
            const batchUpdateSessionPromises = sessionParticipantsToUpdate.map(sessionParticipant => {
              if ('category' in sessionParticipant) {
                const updatedSessionParticipant = { ...sessionParticipant, category: newTitle }
                return new Promise(resolve => {
                  onUpdateSessionParticipant(updatedSessionParticipant)
                  resolve(null)
                })
              }
              return Promise.resolve(null)
            })

            Promise.all(batchUpdateSessionPromises).catch(err => {
              console.error('批量更新屆別參加者時發生錯誤:', err)
            })
          }

          // 顯示完成提示
          if (totalUpdates > 0) {
            alert(`成功更新 ${totalUpdates} 位參加者的職銜從「${oldTitle}」到「${newTitle}」`)
          } else if (hasPlaceholders) {
            alert(`成功更新職銜「${oldTitle}」為「${newTitle}」`)
          } else {
            alert(`成功更新職銜「${oldTitle}」為「${newTitle}」，沒有參加者使用此職銜`)
          }
        } else {
          alert(`沒有找到使用「${oldTitle}」職銜的參加者`)
        }
      } catch (error) {
        console.error('更新職銜時發生錯誤:', error)
        alert(`更新職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
      }
    }
    setEditingTitle("")
  }

  // 處理職銜刪除
  const handleDeleteTitle = (title: string) => {
    try {
      // 找出所有使用該職銜的實際參加者（排除佔位符）
      const participantsToUpdate = allParticipants.filter(p =>
        p.category === title &&
        !p.name.startsWith('職銜佔位符-')
      )

      // 找出使用該職銜的佔位符參加者
      const placeholders = allParticipants.filter(p =>
        p.category === title &&
        p.name.startsWith('職銜佔位符-')
      )

      // 找出所有使用該職銜的屆別參加者
      const sessionParticipantsToUpdate = sessionParticipants.filter(sp =>
        sp.category === title
      )

      const totalUpdates = participantsToUpdate.length
      const hasPlaceholders = placeholders.length > 0

      let confirmMessage = `確定要刪除職銜 "${title}" 嗎？`

      if (totalUpdates > 0) {
        confirmMessage += `\n\n這將會：`
        confirmMessage += `\n• 清除 ${totalUpdates} 位成員的職銜`
        confirmMessage += `\n• 將他們的職銜設為空白`
      }

      if (hasPlaceholders) {
        confirmMessage += totalUpdates > 0 ? `\n• 移除職銜佔位符` : `\n\n這將移除職銜佔位符。`
      }

      if (totalUpdates === 0 && !hasPlaceholders) {
        confirmMessage += `\n\n沒有成員使用此職銜，將直接移除。`
      }

      if (window.confirm(confirmMessage)) {
        try {
          // 使用新的批量刪除函數
          const result = onBulkDeleteTitle(title)

          // 顯示完成提示
          let successMessage = `成功刪除職銜 "${title}"`

          if (result.participantsUpdated > 0) {
            successMessage += `\n\n已完成：`
            successMessage += `\n• 清除了 ${result.participantsUpdated} 位成員的職銜`
            successMessage += `\n• 這些成員的職銜現在為空白`
          }

          if (result.placeholdersRemoved > 0) {
            successMessage += result.participantsUpdated > 0 ? `\n• 移除了職銜佔位符` : `\n\n已移除職銜佔位符。`
          }

          if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {
            successMessage += `\n\n沒有成員使用此職銜，已直接移除。`
          }

          alert(successMessage)
        } catch (error) {
          console.error('刪除職銜時發生錯誤:', error)
          alert(`刪除職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
        }
      }
    } catch (error) {
      console.error('刪除職銜時發生錯誤:', error)
      alert(`刪除職銜時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`)
    }
  }

  // 獲取參加者的歷史屆別信息
  const getParticipantHistory = (participantId: string) => {
    return sessionParticipants
      .filter((sp) => sp.participantId === participantId)
      .map((sp) => ({
        ...sp,
        sessionName: sessions.find((s) => s.id === sp.sessionId)?.name || "未知屆別",
      }))
      .sort((a, b) => b.sessionId.localeCompare(a.sessionId))
  }

  const sortedAllParticipants = useMemo(() => {
    if (!sortField) return allParticipants

    return [...allParticipants].sort((a, b) => {
      let aValue: any = ""
      let bValue: any = ""

      switch (sortField) {
        case "name":
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })
  }, [allParticipants, sortField, sortDirection])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
        >
          ← 返回
        </button>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">參加者管理</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowTitleManagement(true)}
            className="px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            職銜管理
          </button>
          <button
            onClick={() => setShowBulkImport(true)}
            className="px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
              />
            </svg>
            批量導入
          </button>
        </div>
      </div>

      {/* 活躍設定顯示 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
        <div className="text-sm text-blue-800 dark:text-blue-200">
          <strong>當前活躍設定：</strong>
          非常活躍 ≥ {activityLevelSettings.veryActive}%， 活躍 {activityLevelSettings.active}%-
          {activityLevelSettings.veryActive - 1}%， 不活躍 &lt; {activityLevelSettings.active}%
        </div>
      </div>

      {/* 參與度統計 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">參與度統計</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300">整體平均參與度</h4>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {statistics.participationStats.averageParticipationRate.toFixed(1)}%
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              成員參與活動的平均比例
            </p>
          </div>

          {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-700 dark:text-green-300">當前屆別參與度</h4>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1)}%
              </p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName}
              </p>
            </div>
          )}

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">活躍成員數</h4>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId]
                ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants
                : statistics.participantStats.filter(p => p.participationRate > 0).length}
            </p>
            <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
              參與過活動的成員
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
            <h4 className="text-sm font-medium text-orange-700 dark:text-orange-300">高參與度成員</h4>
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {statistics.participationStats.highParticipationStats.count}
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              ≥{statistics.participationStats.highParticipationStats.threshold}% ({statistics.participationStats.highParticipationStats.percentage.toFixed(1)}%)
            </p>
          </div>
        </div>
      </div>

      {/* 職銜管理對話框 */}
      {showTitleManagement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">職銜管理</h3>
                <button
                  onClick={() => setShowTitleManagement(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <TitleManagementSection
                allCategories={allCategories}
                onUpdateTitle={handleUpdateTitle}
                onDeleteTitle={handleDeleteTitle}
              />
            </div>
          </div>
        </div>
      )}

      {/* 批量導入對話框 */}
      {showBulkImport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <UniversalBulkImport
              dataType="participants"
              sessions={sessions}
              selectedSessionId={selectedSessionId}
              onImport={handleBulkImport}
              onCancel={() => setShowBulkImport(false)}
            />
          </div>
        </div>
      )}

      {/* 當前屆別信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-md font-medium text-gray-900 dark:text-white">當前屆別</h3>
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
              {currentSession?.name || "未選擇屆別"}
            </span>
            {currentSession && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {currentSession.startDate} - {currentSession.endDate}
              </span>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            屆別參加者: {currentSessionParticipants.length} 人
            <br />
            所有成員: {allParticipants.length} 人
          </div>
        </div>
      </div>

      {/* 視圖模式切換 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode("session")}
              className={cn(
                "px-4 py-2 rounded-md transition-colors",
                viewMode === "session"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
              )}
            >
              屆別參加者
            </button>
            <button
              onClick={() => setViewMode("global")}
              className={cn(
                "px-4 py-2 rounded-md transition-colors",
                viewMode === "global"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
              )}
            >
              所有成員
            </button>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {viewMode === "session"
              ? `顯示 ${filteredSessionParticipants.length} / ${currentSessionParticipants.length} 位屆別參加者`
              : `顯示 ${allParticipants.length} 位所有成員`}
          </div>
        </div>
      </div>

      {viewMode === "session" ? (
        <>
          {/* 搜索和過濾 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">搜索參加者</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="輸入參加者姓名"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜過濾</label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部職銜</option>
                {currentSessionCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">狀態過濾</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as "all" | "active" | "inactive")}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="all">全部狀態</option>
                <option value="active">活躍</option>
                <option value="inactive">非活躍</option>
              </select>
            </div>
          </div>

          {/* 屆別參加者列表 */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {currentSession?.name} 參加者列表 ({filteredSessionParticipants.length})
              </h3>
            </div>
            <ParticipantTable
              participants={filteredSessionParticipants}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSort}
              onEdit={setEditingSessionParticipant}
              onRemove={onRemoveFromSession}
              onViewHistory={(participant) => {
                const globalParticipant = allParticipants.find(
                  (p) => p.id === participant.participantId,
                )
                if (globalParticipant) {
                  setViewingAttendanceHistory(globalParticipant)
                }
              }}
            />
          </div>

          {/* 添加現有參加者功能 */}
          {availableGlobalParticipants.length > 0 && (
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  添加現有參加者到 {currentSession?.name}
                </h3>
                <button
                  onClick={() => setShowAddExisting(!showAddExisting)}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  {showAddExisting ? "收起" : `添加成員 (${availableGlobalParticipants.length})`}
                </button>
              </div>
            </div>
          )}

          {/* 添加現有參加者對話框 */}
          {showAddExisting && (
            <AddExistingParticipantDialog
              availableParticipants={availableGlobalParticipants}
              allCategories={allCategories}
              onAdd={handleBatchAddToSession}
              onClose={() => setShowAddExisting(false)}
            />
          )}
        </>
      ) : (
        /* 全局參加者視圖 */
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              所有成員列表 ({allParticipants.length})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    姓名
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    職銜
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {allParticipants.map((participant) => (
                  <tr key={participant.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {participant.category || "未設定"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setEditingParticipant(participant)}
                        className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                      >
                        編輯
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`確定要刪除 "${participant.name}" 嗎？這將同時移除其所有屆別記錄。`)) {
                            onDeleteParticipant(participant.id)
                          }
                        }}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                        刪除
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

          {/* 優化後的添加現有參加者功能 */}
          {availableGlobalParticipants.length > 0 && (
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  添加現有參加者到 {currentSession?.name}
                </h3>
                <button
                  onClick={() => setShowAddExisting(!showAddExisting)}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  {showAddExisting ? "收起" : `添加成員 (${availableGlobalParticipants.length})`}
                </button>
              </div>

              {showAddExisting && (
                <div className="space-y-4">
                  {/* 搜索和篩選 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        搜索成員
                      </label>
                      <input
                        type="text"
                        value={addExistingSearchTerm}
                        onChange={(e) => {
                          setAddExistingSearchTerm(e.target.value)
                          setCurrentPage(1)
                        }}
                        placeholder="輸入成員姓名"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        職銜篩選
                      </label>
                      <select
                        value={addExistingCategoryFilter}
                        onChange={(e) => {
                          setAddExistingCategoryFilter(e.target.value)
                          setCurrentPage(1)
                        }}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">全部職銜</option>
                        {allCategories.map((category) => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* 批量操作 */}
                  {selectedParticipants.size > 0 && (
                    <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <span className="text-sm text-blue-800 dark:text-blue-200">
                        已選擇 {selectedParticipants.size} 位成員
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedParticipants(new Set())}
                          className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
                        >
                          清除選擇
                        </button>
                        <button
                          onClick={handleBatchAddToSession}
                          className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                        >
                          批量添加
                        </button>
                      </div>
                    </div>
                  )}

                  {/* 成員列表 */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {filteredAvailableParticipants.participants.map((participant) => (
                      <div
                        key={participant.id}
                        className={cn(
                          "p-3 border rounded-md cursor-pointer transition-colors",
                          selectedParticipants.has(participant.id)
                            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                            : "border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650",
                        )}
                        onClick={() => {
                          const newSelected = new Set(selectedParticipants)
                          if (newSelected.has(participant.id)) {
                            newSelected.delete(participant.id)
                          } else {
                            newSelected.add(participant.id)
                          }
                          setSelectedParticipants(newSelected)
                        }}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{participant.name}</p>
                            {participant.category && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">職銜: {participant.category}</p>
                            )}
                            {(() => {
                              const participantStat = statistics.participantStats.find(p => p.id === participant.id)
                              if (!participantStat) return null

                              return (
                                <div className="mt-1 space-y-1">
                                  <div className="flex items-center space-x-1">
                                    <span className="text-xs text-gray-600 dark:text-gray-400">
                                      總體: {participantStat.participationDetails.participationRatio}
                                    </span>
                                    {participantStat.isHighParticipation && (
                                      <span className="px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded">
                                        高參與
                                      </span>
                                    )}
                                  </div>
                                  {selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && (
                                    <span className="text-xs text-blue-600 dark:text-blue-400">
                                      本屆: {participantStat.sessionParticipationDetails[selectedSessionId].participationRatio}
                                    </span>
                                  )}
                                </div>
                              )
                            })()}
                          </div>
                          <input
                            type="checkbox"
                            checked={selectedParticipants.has(participant.id)}
                            onChange={() => {}}
                            className="ml-2"
                          />
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 分頁 */}
                  {filteredAvailableParticipants.totalPages > 1 && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        顯示 {(currentPage - 1) * pageSize + 1} -{" "}
                        {Math.min(currentPage * pageSize, filteredAvailableParticipants.total)} /{" "}
                        {filteredAvailableParticipants.total} 位成員
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          上一頁
                        </button>
                        <span className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
                          {currentPage} / {filteredAvailableParticipants.totalPages}
                        </span>
                        <button
                          onClick={() =>
                            setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1))
                          }
                          disabled={currentPage === filteredAvailableParticipants.totalPages}
                          className="px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          下一頁
                        </button>
                      </div>
                    </div>
                  )}

                  {filteredAvailableParticipants.participants.length === 0 && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      {availableGlobalParticipants.length > 0 ? "沒有找到符合篩選條件的成員" : "沒有可添加的成員"}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </>
      ) : (
        /* 全局參加者視圖 */
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              所有成員列表 ({allParticipants.length})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <button
                      onClick={() => handleSort("name")}
                      className="flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors"
                    >
                      <span>姓名</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {sortField === "name" ? (
                          sortDirection === "asc" ? (
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 15l7-7 7 7"
                              className="text-blue-500"
                            />
                          ) : (
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                              className="text-blue-500"
                            />
                          )
                        ) : (
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                          />
                        )}
                      </svg>
                    </button>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    參與屆別
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                {sortedAllParticipants.map((participant) => {
                  const history = getParticipantHistory(participant.id)
                  return (
                    <tr key={participant.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {participant.name}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                        <div className="flex flex-wrap gap-1">
                          {history.map((h) => (
                            <span
                              key={h.id}
                              className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded"
                              title={`${h.sessionName}: ${h.category}`}
                            >
                              {h.sessionName}
                            </span>
                          ))}
                          {history.length === 0 && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">未參與任何屆別</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => setViewingAttendanceHistory(participant)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4"
                        >
                          出席記錄
                        </button>
                        <button
                          onClick={() => setEditingParticipant(participant)}
                          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                        >
                          編輯
                        </button>
                        <button
                          onClick={() => {
                            if (window.confirm(`確定要刪除參加者 "${participant.name}" 嗎？這將刪除所有相關數據。`)) {
                              onDeleteParticipant(participant.id)
                            }
                          }}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          刪除
                        </button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 新增參加者表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {editingParticipant ? "編輯成員" : editingSessionParticipant ? "編輯屆別參加者" : "新增參加者"}
        </h3>

        {editingSessionParticipant ? (
          /* 編輯屆別參加者表單 */
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
              <input
                type="text"
                value={editingSessionParticipant.name}
                onChange={(e) => setEditingSessionParticipant({ ...editingSessionParticipant, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜</label>
              <select
                value={editingSessionParticipant.category}
                onChange={(e) =>
                  setEditingSessionParticipant({ ...editingSessionParticipant, category: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">選擇職銜</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">加入日期</label>
              <input
                type="date"
                value={editingSessionParticipant.joinDate || ""}
                onChange={(e) =>
                  setEditingSessionParticipant({ ...editingSessionParticipant, joinDate: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={editingSessionParticipant.isActive}
                  onChange={(e) =>
                    setEditingSessionParticipant({ ...editingSessionParticipant, isActive: e.target.checked })
                  }
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">在此屆別中活躍</span>
              </label>
            </div>
          </div>
        ) : (
          /* 新增/編輯全局參加者表單 */
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
              <input
                type="text"
                value={editingParticipant ? editingParticipant.name : newParticipant.name}
                onChange={(e) =>
                  editingParticipant
                    ? setEditingParticipant({ ...editingParticipant, name: e.target.value })
                    : setNewParticipant({ ...newParticipant, name: e.target.value })
                }
                placeholder="輸入參加者姓名"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">默認職銜</label>
              <select
                value={editingParticipant ? editingParticipant.category || "" : newParticipant.category}
                onChange={(e) =>
                  editingParticipant
                    ? setEditingParticipant({ ...editingParticipant, category: e.target.value })
                    : setNewParticipant({ ...newParticipant, category: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">選擇職銜</option>
                {allCategories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
                <option value="新增職銜">+ 新增職銜</option>
              </select>
            </div>
            {(editingParticipant?.category === "新增職銜" || newParticipant.category === "新增職銜") && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">新職銜名稱</label>
                <input
                  type="text"
                  value={editingParticipant ? (editingParticipant.category === "新增職銜" ? "" : editingParticipant.category) : newParticipantTitle}
                  placeholder="輸入新職銜名稱"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  onChange={(e) =>
                    editingParticipant
                      ? setEditingParticipant({ ...editingParticipant, category: e.target.value })
                      : setNewParticipantTitle(e.target.value)
                  }
                />
              </div>
            )}
          </div>
        )}

        <div className="mt-4 flex justify-end space-x-3">
          {(editingParticipant || editingSessionParticipant) && (
            <button
              onClick={() => {
                setEditingParticipant(null)
                setEditingSessionParticipant(null)
              }}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              取消
            </button>
          )}
          <button
            onClick={
              editingSessionParticipant
                ? handleUpdateSessionParticipant
                : editingParticipant
                  ? handleUpdateParticipant
                  : handleAddParticipant
            }
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            disabled={
              isAddingParticipant ||
              (editingSessionParticipant
                ? !editingSessionParticipant.name.trim() || !editingSessionParticipant.category
                : editingParticipant
                  ? !editingParticipant.name.trim()
                  : !newParticipant.name.trim() || (newParticipant.category === "新增職銜" && !newParticipantTitle.trim()))
            }
          >
            {editingSessionParticipant
              ? "更新屆別參加者"
              : editingParticipant
                ? "更新成員"
                : isAddingParticipant
                  ? "新增中..."
                  : "新增參加者"}
          </button>
        </div>
      </div>

      {/* 統計信息 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">統計信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{allParticipants.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">所有成員總數</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">{currentSessionParticipants.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">當前屆別參加者</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {currentSessionParticipants.filter((sp) => sp.isActive).length}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">活躍參加者</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{currentSessionCategories.length}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">當前屆別職銜數</p>
          </div>
        </div>
      </div>

      {/* 出席記錄模態框 */}
      {viewingAttendanceHistory && (
        <ParticipantAttendanceHistory
          participant={viewingAttendanceHistory}
          activities={activities}
          sessionParticipants={sessionParticipants}
          sessions={sessions}
          onClose={() => setViewingAttendanceHistory(null)}
        />
      )}
    </div>
  )
}
