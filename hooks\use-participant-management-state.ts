import { useState } from "react"
import type { Participant, SessionParticipant } from "../types"

/**
 * 參加者管理狀態管理 Hook
 * 用途：集中管理參加者管理頁面的所有狀態
 */
export function useParticipantManagementState() {
  // 新增參加者相關狀態
  const [newParticipant, setNewParticipant] = useState({
    name: "",
    category: "",
  })
  const [newParticipantTitle, setNewParticipantTitle] = useState("")
  const [isAddingParticipant, setIsAddingParticipant] = useState(false)

  // 編輯相關狀態
  const [editingParticipant, setEditingParticipant] = useState<Participant | null>(null)
  const [editingSessionParticipant, setEditingSessionParticipant] = useState<SessionParticipant | null>(null)

  // 搜索和過濾狀態
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all")

  // 對話框顯示狀態
  const [showBulkImport, setShowBulkImport] = useState(false)
  const [showTitleManagement, setShowTitleManagement] = useState(false)
  const [showAddExisting, setShowAddExisting] = useState(false)

  // 視圖和排序狀態
  const [viewMode, setViewMode] = useState<"session" | "global">("session")
  const [viewingAttendanceHistory, setViewingAttendanceHistory] = useState<Participant | null>(null)
  const [sortField, setSortField] = useState<string>("")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

  return {
    // 新增參加者狀態
    newParticipant,
    setNewParticipant,
    newParticipantTitle,
    setNewParticipantTitle,
    isAddingParticipant,
    setIsAddingParticipant,

    // 編輯狀態
    editingParticipant,
    setEditingParticipant,
    editingSessionParticipant,
    setEditingSessionParticipant,

    // 搜索和過濾狀態
    searchTerm,
    setSearchTerm,
    categoryFilter,
    setCategoryFilter,
    statusFilter,
    setStatusFilter,

    // 對話框狀態
    showBulkImport,
    setShowBulkImport,
    showTitleManagement,
    setShowTitleManagement,
    showAddExisting,
    setShowAddExisting,

    // 視圖和排序狀態
    viewMode,
    setViewMode,
    viewingAttendanceHistory,
    setViewingAttendanceHistory,
    sortField,
    setSortField,
    sortDirection,
    setSortDirection,
  }
}
