import type { Participant, SessionParticipant } from "../../types"

interface ParticipantManagementFormProps {
  // 新增參加者狀態
  newParticipant: { name: string; category: string }
  newParticipantTitle: string
  isAddingParticipant: boolean
  allCategories: string[]

  // 編輯狀態
  editingParticipant: Participant | null
  editingSessionParticipant: SessionParticipant | null

  // 事件處理
  onNewParticipantChange: (participant: { name: string; category: string }) => void
  onNewParticipantTitleChange: (title: string) => void
  onAddParticipant: () => void
  onEditingParticipantChange: (participant: Participant | null) => void
  onEditingSessionParticipantChange: (participant: SessionParticipant | null) => void
  onUpdateParticipant: () => void
  onUpdateSessionParticipant: () => void
}

/**
 * 參加者管理表單組件
 * 用途：處理新增和編輯參加者的表單
 */
export function ParticipantManagementForm({
  newParticipant,
  newParticipantTitle,
  isAddingParticipant,
  allCategories,
  editingParticipant,
  editingSessionParticipant,
  onNewParticipantChange,
  onNewParticipantTitleChange,
  onAddParticipant,
  onEditingParticipantChange,
  onEditingSessionParticipantChange,
  onUpdateParticipant,
  onUpdateSessionParticipant,
}: ParticipantManagementFormProps) {
  return (
    <>
      {/* 新增參加者表單 */}
      <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">新增參加者</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
            <input
              type="text"
              value={newParticipant.name}
              onChange={(e) => onNewParticipantChange({ ...newParticipant, name: e.target.value })}
              placeholder="輸入參加者姓名"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜</label>
            <select
              value={newParticipant.category}
              onChange={(e) => onNewParticipantChange({ ...newParticipant, category: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            >
              <option value="">選擇職銜</option>
              {allCategories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
              <option value="新增職銜">+ 新增職銜</option>
            </select>
          </div>
          {newParticipant.category === "新增職銜" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">新職銜名稱</label>
              <input
                type="text"
                value={newParticipantTitle}
                onChange={(e) => onNewParticipantTitleChange(e.target.value)}
                placeholder="輸入新職銜名稱"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>
          )}
        </div>
        <div className="mt-4">
          <button
            onClick={onAddParticipant}
            disabled={!newParticipant.name.trim() || isAddingParticipant || (newParticipant.category === "新增職銜" && !newParticipantTitle.trim())}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            {isAddingParticipant ? "新增中..." : "新增參加者"}
          </button>
        </div>
      </div>

      {/* 編輯參加者對話框 */}
      {editingParticipant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">編輯參加者</h3>
                <button
                  onClick={() => onEditingParticipantChange(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
                  <input
                    type="text"
                    value={editingParticipant.name}
                    onChange={(e) => onEditingParticipantChange({ ...editingParticipant, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜</label>
                  <select
                    value={editingParticipant.category || ""}
                    onChange={(e) => onEditingParticipantChange({ ...editingParticipant, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">選擇職銜</option>
                    {allCategories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => onEditingParticipantChange(null)}
                  className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={onUpdateParticipant}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  更新
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 編輯屆別參加者對話框 */}
      {editingSessionParticipant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">編輯屆別參加者</h3>
                <button
                  onClick={() => onEditingSessionParticipantChange(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">姓名</label>
                  <input
                    type="text"
                    value={editingSessionParticipant.name}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">職銜</label>
                  <input
                    type="text"
                    value={editingSessionParticipant.category || ""}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">加入日期</label>
                  <input
                    type="date"
                    value={editingSessionParticipant.joinDate}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, joinDate: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={editingSessionParticipant.isActive}
                    onChange={(e) => onEditingSessionParticipantChange({ ...editingSessionParticipant, isActive: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor="isActive" className="text-sm text-gray-700 dark:text-gray-300">
                    活躍狀態
                  </label>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => onEditingSessionParticipantChange(null)}
                  className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={onUpdateSessionParticipant}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  更新
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
