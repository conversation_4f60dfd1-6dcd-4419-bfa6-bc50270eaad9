import { useMemo } from "react"
import type { Activity, Participant, Session } from "../types"

interface UseDashboardDataProps {
  activities: Activity[]
  allParticipants: Participant[]
  sessions: Session[]
}

export function useDashboardData({ activities, allParticipants, sessions }: UseDashboardDataProps) {
  // 計算KPI數據
  const kpis = useMemo(() => {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear

    // 本月活動數
    const thisMonthActivities = activities.filter(activity => {
      const activityDate = new Date(activity.date)
      return activityDate.getMonth() === currentMonth && activityDate.getFullYear() === currentYear
    }).length

    // 上月活動數
    const lastMonthActivities = activities.filter(activity => {
      const activityDate = new Date(activity.date)
      return activityDate.getMonth() === lastMonth && activityDate.getFullYear() === lastMonthYear
    }).length

    // 本月平均出席率
    const thisMonthActivitiesData = activities.filter(activity => {
      const activityDate = new Date(activity.date)
      return activityDate.getMonth() === currentMonth && activityDate.getFullYear() === currentYear
    })

    const thisMonthAvgAttendance = thisMonthActivitiesData.length > 0
      ? thisMonthActivitiesData.reduce((sum, activity) => {
          const totalParticipants = activity.participants.length
          const attendedCount = activity.participants.filter(p => {
            const attendance = p.attendance
            return attendance[activity.id] === true || attendance[activity.date] === true
          }).length
          return sum + (totalParticipants > 0 ? (attendedCount / totalParticipants) * 100 : 0)
        }, 0) / thisMonthActivitiesData.length
      : 0

    // 上月平均出席率
    const lastMonthActivitiesData = activities.filter(activity => {
      const activityDate = new Date(activity.date)
      return activityDate.getMonth() === lastMonth && activityDate.getFullYear() === lastMonthYear
    })

    const lastMonthAvgAttendance = lastMonthActivitiesData.length > 0
      ? lastMonthActivitiesData.reduce((sum, activity) => {
          const totalParticipants = activity.participants.length
          const attendedCount = activity.participants.filter(p => {
            const attendance = p.attendance
            return attendance[activity.id] === true || attendance[activity.date] === true
          }).length
          return sum + (totalParticipants > 0 ? (attendedCount / totalParticipants) * 100 : 0)
        }, 0) / lastMonthActivitiesData.length
      : 0

    const attendanceChange = thisMonthAvgAttendance - lastMonthAvgAttendance

    // 活躍參與者數量（參與率 > 50%）
    const activeParticipants = allParticipants.filter(participant => {
      const participantActivities = activities.filter(activity =>
        activity.participants.some(p => p.id === participant.id)
      )
      
      if (participantActivities.length === 0) return false
      
      const attendedCount = participantActivities.filter(activity => {
        const participantInActivity = activity.participants.find(p => p.id === participant.id)
        if (!participantInActivity) return false
        
        const attendance = participantInActivity.attendance
        return attendance[activity.id] === true || attendance[activity.date] === true
      }).length
      
      const participationRate = (attendedCount / participantActivities.length) * 100
      return participationRate > 50
    }).length

    const engagementRate = allParticipants.length > 0
      ? (activeParticipants / allParticipants.length) * 100
      : 0

    return {
      thisMonthActivities: Math.round(thisMonthActivities),
      lastMonthActivities: Math.round(lastMonthActivities),
      thisMonthAvgAttendance: Math.round(thisMonthAvgAttendance),
      attendanceChange: Math.round(attendanceChange * 10) / 10,
      engagementRate: Math.round(engagementRate),
      activeParticipants,
    }
  }, [activities, allParticipants])

  // 計算月度趨勢數據
  const monthlyData = useMemo(() => {
    const monthlyStats: { [key: string]: { activities: number; attendance: number; participants: Set<string> } } = {}
    
    activities.forEach(activity => {
      const date = new Date(activity.date)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      if (!monthlyStats[monthKey]) {
        monthlyStats[monthKey] = { activities: 0, attendance: 0, participants: new Set() }
      }
      
      monthlyStats[monthKey].activities++
      
      // 計算出席率
      const totalParticipants = activity.participants.length
      const attendedCount = activity.participants.filter(p => {
        const attendance = p.attendance
        return attendance[activity.id] === true || attendance[activity.date] === true
      }).length
      
      const attendanceRate = totalParticipants > 0 ? (attendedCount / totalParticipants) * 100 : 0
      monthlyStats[monthKey].attendance += attendanceRate
      
      // 記錄參與者
      activity.participants.forEach(p => {
        monthlyStats[monthKey].participants.add(p.id)
      })
    })
    
    return Object.entries(monthlyStats)
      .map(([month, stats]) => ({
        month: month,
        activities: stats.activities,
        attendance: Math.round(stats.attendance / stats.activities),
        participants: stats.participants.size,
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
      .slice(-6) // 最近6個月
  }, [activities])

  // 計算週度出席率趨勢
  const weeklyAttendance = useMemo(() => {
    const weeklyStats: { [key: string]: { totalRate: number; count: number } } = {}
    
    activities.forEach(activity => {
      const date = new Date(activity.date)
      const weekStart = new Date(date)
      weekStart.setDate(date.getDate() - date.getDay()) // 週日為一週開始
      const weekKey = weekStart.toISOString().split('T')[0]
      
      if (!weeklyStats[weekKey]) {
        weeklyStats[weekKey] = { totalRate: 0, count: 0 }
      }
      
      const totalParticipants = activity.participants.length
      const attendedCount = activity.participants.filter(p => {
        const attendance = p.attendance
        return attendance[activity.id] === true || attendance[activity.date] === true
      }).length
      
      const attendanceRate = totalParticipants > 0 ? (attendedCount / totalParticipants) * 100 : 0
      weeklyStats[weekKey].totalRate += attendanceRate
      weeklyStats[weekKey].count++
    })
    
    return Object.entries(weeklyStats)
      .map(([week, stats]) => ({
        week: new Date(week).toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' }),
        rate: Math.round(stats.totalRate / stats.count),
      }))
      .sort((a, b) => a.week.localeCompare(b.week))
      .slice(-8) // 最近8週
  }, [activities])

  // 生成智能洞察
  const insights = useMemo(() => {
    const insights: Array<{
      type: "success" | "warning" | "info" | "error"
      title: string
      description: string
      action?: string
    }> = []

    // 出席率洞察
    if (kpis.thisMonthAvgAttendance >= 85) {
      insights.push({
        type: "success",
        title: "出席率表現優秀",
        description: `本月平均出席率達到 ${kpis.thisMonthAvgAttendance}%，表現非常好！`,
      })
    } else if (kpis.thisMonthAvgAttendance < 60) {
      insights.push({
        type: "warning",
        title: "出席率需要關注",
        description: `本月平均出席率僅 ${kpis.thisMonthAvgAttendance}%，低於理想水平。`,
        action: "考慮調整活動時間或提高活動吸引力",
      })
    }

    // 活動數量洞察
    if (kpis.thisMonthActivities > kpis.lastMonthActivities) {
      insights.push({
        type: "info",
        title: "活動數量增加",
        description: `本月活動數量比上月增加了 ${kpis.thisMonthActivities - kpis.lastMonthActivities} 個。`,
      })
    } else if (kpis.thisMonthActivities < kpis.lastMonthActivities) {
      insights.push({
        type: "info",
        title: "活動數量減少",
        description: `本月活動數量比上月減少了 ${kpis.lastMonthActivities - kpis.thisMonthActivities} 個。`,
      })
    }

    // 參與度洞察
    if (kpis.engagementRate >= 70) {
      insights.push({
        type: "success",
        title: "參與度很高",
        description: `${kpis.engagementRate}% 的成員保持活躍參與，社群活力很好！`,
      })
    } else if (kpis.engagementRate < 40) {
      insights.push({
        type: "warning",
        title: "參與度偏低",
        description: `只有 ${kpis.engagementRate}% 的成員保持活躍參與。`,
        action: "考慮舉辦更多吸引人的活動或改善溝通方式",
      })
    }

    return insights
  }, [kpis])

  return {
    kpis,
    monthlyData,
    weeklyAttendance,
    insights,
  }
}
