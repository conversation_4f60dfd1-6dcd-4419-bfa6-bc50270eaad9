"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"

interface TitleManagementSectionProps {
  allCategories: string[]
  onUpdateTitle: (oldTitle: string, newTitle: string) => void
  onDeleteTitle: (title: string) => void
}

export function TitleManagementSection({
  allCategories,
  onUpdateTitle,
  onDeleteTitle,
}: TitleManagementSectionProps) {
  const [editingTitle, setEditingTitle] = useState("")
  const [newTitle, setNewTitle] = useState("")

  const handleUpdateTitle = (oldTitle: string) => {
    if (newTitle.trim() && newTitle !== oldTitle) {
      onUpdateTitle(oldTitle, newTitle.trim())
      setEditingTitle("")
      setNewTitle("")
    }
  }

  const handleDeleteTitle = (title: string) => {
    if (window.confirm(`確定要刪除職銜「${title}」嗎？這將影響所有使用此職銜的參加者。`)) {
      onDeleteTitle(title)
    }
  }

  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">職銜管理</h3>
      
      {allCategories.length > 0 ? (
        <div className="space-y-2">
          {allCategories.map((category) => (
            <div
              key={category}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg"
            >
              <div className="flex-1">
                {editingTitle === category ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newTitle}
                      onChange={(e) => setNewTitle(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleUpdateTitle(category)
                        } else if (e.key === "Escape") {
                          setEditingTitle("")
                          setNewTitle("")
                        }
                      }}
                      placeholder="輸入新職銜名稱"
                      className="flex-1 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
                      autoFocus
                    />
                    <button
                      onClick={() => handleUpdateTitle(category)}
                      className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm"
                    >
                      保存
                    </button>
                    <button
                      onClick={() => {
                        setEditingTitle("")
                        setNewTitle("")
                      }}
                      className="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                    >
                      取消
                    </button>
                  </div>
                ) : (
                  <span className="text-gray-900 dark:text-white font-medium">{category}</span>
                )}
              </div>
              
              {editingTitle !== category && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setEditingTitle(category)
                      setNewTitle(category)
                    }}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                  >
                    編輯
                  </button>
                  <button
                    onClick={() => handleDeleteTitle(category)}
                    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm"
                  >
                    刪除
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">沒有職銜</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            當您添加參加者時，系統會自動創建職銜
          </p>
        </div>
      )}
      
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">職銜管理說明</h3>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
              <ul className="list-disc list-inside space-y-1">
                <li>編輯職銜會同時更新所有使用該職銜的參加者</li>
                <li>刪除職銜會將使用該職銜的參加者職銜設為空白</li>
                <li>職銜名稱不能重複</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
