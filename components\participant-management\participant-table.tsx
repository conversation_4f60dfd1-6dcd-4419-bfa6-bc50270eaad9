"use client"

import { cn } from "@/lib/utils"
import type { SessionParticipant } from "../../types"
import { getActivityLevelText, getActivityLevelColor } from "../../utils/activity-level"

interface ParticipantWithStats extends SessionParticipant {
  attendanceRate: number
  activityLevel: string
  totalParticipated: number
  totalAttended: number
}

interface ParticipantTableProps {
  participants: ParticipantWithStats[]
  sortField: string
  sortDirection: "asc" | "desc"
  onSort: (field: string) => void
  onEdit: (participant: SessionParticipant) => void
  onRemove: (participantId: string) => void
  onViewHistory: (participant: any) => void
}

export function ParticipantTable({
  participants,
  sortField,
  sortDirection,
  onSort,
  onEdit,
  onRemove,
  onViewHistory,
}: ParticipantTableProps) {
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      )
    }
    return sortDirection === "asc" ? (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => onSort("name")}
              >
                <div className="flex items-center">
                  姓名
                  {getSortIcon("name")}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => onSort("category")}
              >
                <div className="flex items-center">
                  職銜
                  {getSortIcon("category")}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => onSort("joinDate")}
              >
                <div className="flex items-center">
                  加入日期
                  {getSortIcon("joinDate")}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => onSort("status")}
              >
                <div className="flex items-center">
                  狀態
                  {getSortIcon("status")}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => onSort("attendanceRate")}
              >
                <div className="flex items-center">
                  出席率
                  {getSortIcon("attendanceRate")}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                活躍等級
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
            {participants.length > 0 ? (
              participants.map((participant) => (
                <tr key={participant.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {participant.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {participant.category || "未設定"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {participant.joinDate || "未設定"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        participant.isActive
                          ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                          : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
                      )}
                    >
                      {participant.isActive ? "活躍" : "非活躍"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <div className="flex items-center">
                      <span className="mr-2">{participant.attendanceRate.toFixed(1)}%</span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className={cn(
                            "h-2 rounded-full",
                            participant.attendanceRate >= 80
                              ? "bg-green-500"
                              : participant.attendanceRate >= 60
                                ? "bg-yellow-500"
                                : "bg-red-500",
                          )}
                          style={{ width: `${Math.min(participant.attendanceRate, 100)}%` }}
                        />
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {participant.totalAttended}/{participant.totalParticipated} 次
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        getActivityLevelColor(participant.activityLevel),
                      )}
                    >
                      {getActivityLevelText(participant.activityLevel)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <button
                      onClick={() => onViewHistory(participant)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                    >
                      歷史
                    </button>
                    <button
                      onClick={() => onEdit(participant)}
                      className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                    >
                      編輯
                    </button>
                    <button
                      onClick={() => {
                        if (window.confirm(`確定要從當前屆別移除 "${participant.name}" 嗎？`)) {
                          onRemove(participant.id)
                        }
                      }}
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                    >
                      移除
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  沒有找到符合條件的參加者
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
